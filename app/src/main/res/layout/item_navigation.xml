<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="0dp"
    android:layout_height="120dp"
    android:layout_margin="4dp"
    app:cardElevation="0dp"
    app:cardCornerRadius="12dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="16dp">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginBottom="8dp"
            tools:src="@drawable/ic_faqs" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="سوالات متداول" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
