<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="0dp"
    android:layout_height="80dp"
    android:layout_margin="4dp"
    app:cardElevation="0dp"
    app:cardCornerRadius="12dp"
    app:cardBackgroundColor="@color/home_box_groups_bg"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_groups"
            android:layout_marginEnd="8dp"
            app:tint="@color/home_box_groups_fg" />

        <TextView
            android:id="@+id/tv_group_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/home_box_groups_fg"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="اداره کل" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
