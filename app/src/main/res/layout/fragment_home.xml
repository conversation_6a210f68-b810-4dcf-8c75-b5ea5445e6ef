<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="8dp"
    tools:context=".ui.home.HomeFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Section 1: Tickets -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="تیکت‌ها"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp"
            android:textColor="?attr/colorOnSurface" />

        <!-- All Tickets Box -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_all_tickets"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_marginBottom="8dp"
            app:cardElevation="0dp"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="@color/home_box_tickets_bg"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_tickets"
                    android:layout_marginEnd="12dp"
                    app:tint="@color/home_box_tickets_fg" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="همه تیکت‌ها"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/home_box_tickets_fg" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Dynamic Ticket Statistics Grid -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_ticket_statistics"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp" />

        <!-- Dynamic Groups Grid -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_groups"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp" />

        <!-- Loading indicator -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

        <!-- Error message -->
        <TextView
            android:id="@+id/error_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="خطا در بارگذاری اطلاعات"
            android:textSize="14sp"
            android:textColor="?attr/colorError"
            android:visibility="gone" />

        <!-- Section 2: Navigation -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="منوها"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp"
            android:textColor="?attr/colorOnSurface" />

        <!-- Navigation Grid -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
