<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_home">

    <fragment
        android:id="@+id/nav_home"
        android:name="com.ariel.app.ui.home.HomeFragment"
        android:label="@string/menu_home"
        tools:layout="@layout/fragment_home">
        <action
            android:id="@+id/action_nav_home_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment" />
        <action
            android:id="@+id/action_nav_home_to_nav_tickets"
            app:destination="@id/nav_tickets" />
    </fragment>

    <fragment
        android:id="@+id/nav_tickets"
        android:name="com.ariel.app.ui.tickets.TicketsFragment"
        android:label="@string/menu_tickets"
        tools:layout="@layout/fragment_tickets">
        <argument
            android:name="status"
            app:argType="string"
            app:nullable="true"
            android:defaultValue="@null" />
        <argument
            android:name="groupId"
            app:argType="integer"
            android:defaultValue="-1" />
        <action
            android:id="@+id/action_nav_tickets_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment" />
        <action
            android:id="@+id/action_nav_tickets_to_nav_new_ticket"
            app:destination="@id/nav_new_ticket" />
    </fragment>

    <fragment
        android:id="@+id/ticketDetailsFragment"
        android:name="com.ariel.app.ui.ticketdetails.TicketDetailsFragment"
        android:label="@string/ticket_details"
        tools:layout="@layout/fragment_ticket_details">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <action
            android:id="@+id/action_ticketDetailsFragment_to_nav_tickets"
            app:destination="@id/nav_tickets"
            app:popUpTo="@id/nav_tickets"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/nav_new_ticket"
        android:name="com.ariel.app.ui.newticket.NewTicketFragment"
        android:label="@string/new_ticket"
        tools:layout="@layout/fragment_new_ticket">
        <action
            android:id="@+id/action_nav_new_ticket_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_profile"
        android:name="com.ariel.app.ui.profile.ProfileFragment"
        android:label="@string/menu_profile"
        tools:layout="@layout/fragment_profile" />

    <fragment
        android:id="@+id/nav_knowledges"
        android:name="com.ariel.app.ui.knowledges.KnowledgesFragment"
        android:label="@string/menu_knowledges"
        tools:layout="@layout/fragment_knowledges">
        <action
            android:id="@+id/action_nav_knowledges_to_knowledgeDetailsFragment"
            app:destination="@id/knowledgeDetailsFragment" />
        <action
            android:id="@+id/action_nav_knowledges_to_nav_new_knowledge"
            app:destination="@id/nav_new_knowledge" />
    </fragment>

    <fragment
        android:id="@+id/knowledgeDetailsFragment"
        android:name="com.ariel.app.ui.knowledgedetails.KnowledgeDetailsFragment"
        android:label="@string/knowledge_details"
        tools:layout="@layout/fragment_knowledge_details">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <action
            android:id="@+id/action_knowledgeDetailsFragment_to_editKnowledgeFragment"
            app:destination="@id/editKnowledgeFragment" />
    </fragment>

    <fragment
        android:id="@+id/editKnowledgeFragment"
        android:name="com.ariel.app.ui.editknowledge.EditKnowledgeFragment"
        android:label="@string/edit"
        tools:layout="@layout/fragment_edit_knowledge">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <argument
            android:name="knowledge"
            app:argType="com.ariel.app.data.model.Knowledge" />
        <action
            android:id="@+id/action_editKnowledgeFragment_to_knowledgeDetailsFragment"
            app:destination="@id/knowledgeDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_new_knowledge"
        android:name="com.ariel.app.ui.newknowledge.NewKnowledgeFragment"
        android:label="@string/new_knowledge"
        tools:layout="@layout/fragment_new_knowledge">
        <action
            android:id="@+id/action_nav_new_knowledge_to_knowledgeDetailsFragment"
            app:destination="@id/knowledgeDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_faqs"
        android:name="com.ariel.app.ui.faqs.FAQsFragment"
        android:label="@string/menu_faqs"
        tools:layout="@layout/fragment_faqs">
        <action
            android:id="@+id/action_nav_faqs_to_faqDetailsFragment"
            app:destination="@id/faqDetailsFragment" />
        <action
            android:id="@+id/action_nav_faqs_to_nav_new_faq"
            app:destination="@id/nav_new_faq" />
    </fragment>

    <fragment
        android:id="@+id/faqDetailsFragment"
        android:name="com.ariel.app.ui.faqdetails.FAQDetailsFragment"
        android:label="@string/faq_details"
        tools:layout="@layout/fragment_faq_details">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <action
            android:id="@+id/action_faqDetailsFragment_to_editFAQFragment"
            app:destination="@id/editFAQFragment" />
    </fragment>

    <fragment
        android:id="@+id/editFAQFragment"
        android:name="com.ariel.app.ui.editfaq.EditFAQFragment"
        android:label="@string/edit"
        tools:layout="@layout/fragment_edit_faq">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <argument
            android:name="faq"
            app:argType="com.ariel.app.data.model.FAQ" />
        <action
            android:id="@+id/action_editFAQFragment_to_faqDetailsFragment"
            app:destination="@id/faqDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_new_faq"
        android:name="com.ariel.app.ui.newfaq.NewFAQFragment"
        android:label="@string/new_faq"
        tools:layout="@layout/fragment_new_faq">
        <action
            android:id="@+id/action_nav_new_faq_to_faqDetailsFragment"
            app:destination="@id/faqDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_events"
        android:name="com.ariel.app.ui.events.EventsFragment"
        android:label="@string/menu_events"
        tools:layout="@layout/fragment_events">
        <action
            android:id="@+id/action_nav_events_to_eventDetailsFragment"
            app:destination="@id/eventDetailsFragment" />
        <action
            android:id="@+id/action_nav_events_to_nav_new_event"
            app:destination="@id/nav_new_event" />
    </fragment>

    <fragment
        android:id="@+id/eventDetailsFragment"
        android:name="com.ariel.app.ui.eventdetails.EventDetailsFragment"
        android:label="@string/event_details"
        tools:layout="@layout/fragment_event_details">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <action
            android:id="@+id/action_eventDetailsFragment_to_editEventFragment"
            app:destination="@id/editEventFragment" />
    </fragment>

    <fragment
        android:id="@+id/editEventFragment"
        android:name="com.ariel.app.ui.editevent.EditEventFragment"
        android:label="@string/edit"
        tools:layout="@layout/fragment_edit_event">
        <argument
            android:name="shortUuid"
            app:argType="string" />
        <argument
            android:name="event"
            app:argType="com.ariel.app.data.model.Event" />
        <action
            android:id="@+id/action_editEventFragment_to_eventDetailsFragment"
            app:destination="@id/eventDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_new_event"
        android:name="com.ariel.app.ui.newevent.NewEventFragment"
        android:label="@string/new_event"
        tools:layout="@layout/fragment_new_event">
        <action
            android:id="@+id/action_nav_new_event_to_eventDetailsFragment"
            app:destination="@id/eventDetailsFragment"
            app:popUpTo="@id/nav_home"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/nav_users"
        android:name="com.ariel.app.ui.users.UsersFragment"
        android:label="@string/menu_users"
        tools:layout="@layout/fragment_users" />

    <fragment
        android:id="@+id/searchResultsFragment"
        android:name="com.ariel.app.ui.search.SearchResultsFragment"
        android:label="@string/search_results"
        tools:layout="@layout/fragment_search_results">
        <argument
            android:name="query"
            app:argType="string" />
        <action
            android:id="@+id/action_searchResultsFragment_to_ticketDetailsFragment"
            app:destination="@id/ticketDetailsFragment" />
        <action
            android:id="@+id/action_searchResultsFragment_to_eventDetailsFragment"
            app:destination="@id/eventDetailsFragment" />
        <action
            android:id="@+id/action_searchResultsFragment_to_knowledgeDetailsFragment"
            app:destination="@id/knowledgeDetailsFragment" />
        <action
            android:id="@+id/action_searchResultsFragment_to_faqDetailsFragment"
            app:destination="@id/faqDetailsFragment" />
    </fragment>
</navigation>
