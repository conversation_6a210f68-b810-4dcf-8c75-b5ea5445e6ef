package com.ariel.app.ui.tickets

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.R
import com.ariel.app.data.SessionManager
import com.ariel.app.databinding.FragmentTicketsBinding

/**
 * Fragment for displaying a list of tickets.
 */
class TicketsFragment : Fragment() {

    private var _binding: FragmentTicketsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: TicketsViewModel
    private lateinit var ticketAdapter: TicketAdapter
    private lateinit var sessionManager: SessionManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(TicketsViewModel::class.java)
        _binding = FragmentTicketsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        setupRecyclerView()
        setupNewTicketButton()
        observeViewModel()
        fetchTickets()
    }

    /**
     * Sets up the new ticket button.
     * Only shows the button for regular users, not for superusers.
     */
    private fun setupNewTicketButton() {
        val user = sessionManager.getUser()
        if (user != null && !user.isSuperuser) {
            // Only regular users can create tickets
            binding.fabNewTicket.setOnClickListener {
                try {
                    // Show a toast to indicate we're trying to navigate
                    Toast.makeText(requireContext(), "Navigating to New Ticket page...", Toast.LENGTH_SHORT).show()

                    // Navigate to the new ticket page
                    findNavController().navigate(R.id.action_nav_tickets_to_nav_new_ticket)
                } catch (e: Exception) {
                    // If navigation fails, show the error
                    Toast.makeText(requireContext(), "Navigation error: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("TicketsFragment", "Navigation error", e)
                }
            }
            binding.fabNewTicket.visibility = View.VISIBLE
        } else {
            // Hide the button for superusers
            binding.fabNewTicket.visibility = View.GONE
        }
    }

    /**
     * Sets up the RecyclerView for displaying tickets with pagination.
     */
    private fun setupRecyclerView() {
        ticketAdapter = TicketAdapter { ticket ->
            // Navigate to ticket details
            val bundle = Bundle().apply {
                putString("shortUuid", ticket.shortUuid)
            }
            findNavController().navigate(R.id.action_nav_tickets_to_ticketDetailsFragment, bundle)
        }

        val layoutManager = LinearLayoutManager(requireContext())
        binding.recyclerTickets.apply {
            this.layoutManager = layoutManager
            adapter = ticketAdapter

            // Add scroll listener for pagination
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    val visibleItemCount = layoutManager.childCount
                    val totalItemCount = layoutManager.itemCount
                    val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                    // Check if we're near the bottom and should load more
                    val isLoadingMore = viewModel.isLoadingMore.value ?: false
                    if (!isLoadingMore && viewModel.hasMorePages()) {
                        if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 3) {
                            // Load more when 3 items from the bottom
                            loadMoreTickets()
                        }
                    }
                }
            })
        }
    }

    /**
     * Observes changes in the ViewModel.
     */
    private fun observeViewModel() {
        viewModel.tickets.observe(viewLifecycleOwner) { tickets ->
            ticketAdapter.submitList(tickets)

            // Show empty view if the list is empty
            binding.tvEmpty.visibility = if (tickets.isEmpty()) View.VISIBLE else View.GONE
            binding.recyclerTickets.visibility = if (tickets.isEmpty()) View.GONE else View.VISIBLE
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE

            if (isLoading) {
                binding.tvError.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            }
        }

        viewModel.isLoadingMore.observe(viewLifecycleOwner) { isLoadingMore ->
            binding.progressBarPagination.visibility = if (isLoadingMore) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvError.text = errorMessage
                binding.tvError.visibility = View.VISIBLE
                binding.recyclerTickets.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            } else {
                binding.tvError.visibility = View.GONE
            }
        }
    }

    /**
     * Fetches tickets from the API.
     */
    private fun fetchTickets() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            // Check if we have filter arguments
            val status = arguments?.getString("status")
            val groupId = arguments?.getInt("groupId", -1)?.takeIf { it != -1 }

            when {
                status != null -> {
                    Log.d("TicketsFragment", "Fetching tickets with status filter: $status")
                    viewModel.fetchTicketsByStatus(token, status)
                }
                groupId != null -> {
                    Log.d("TicketsFragment", "Fetching tickets with group filter: $groupId")
                    viewModel.fetchTicketsByGroupId(token, groupId)
                }
                else -> {
                    Log.d("TicketsFragment", "Fetching all tickets")
                    viewModel.fetchTickets(token)
                }
            }
        } else {
            binding.tvError.text = "Authentication token not found"
            binding.tvError.visibility = View.VISIBLE
            binding.recyclerTickets.visibility = View.GONE
        }
    }

    /**
     * Loads more tickets for pagination.
     */
    private fun loadMoreTickets() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.loadMoreTickets(token)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
