package com.ariel.app.ui.home

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import com.ariel.app.ArielApplication
import com.ariel.app.R
import com.ariel.app.data.SessionManager
import com.ariel.app.data.model.EventGroup
import com.ariel.app.data.model.TicketStatistics
import com.ariel.app.databinding.FragmentHomeBinding

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var sessionManager: SessionManager

    // Adapters
    private lateinit var ticketStatisticsAdapter: TicketStatisticsAdapter
    private lateinit var groupAdapter: HomeGroupAdapter
    private lateinit var navigationAdapter: NavigationAdapter

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        sessionManager = (requireActivity().application as ArielApplication).sessionManager
        val factory = HomeViewModelFactory(sessionManager)
        homeViewModel = ViewModelProvider(this, factory)[HomeViewModel::class.java]

        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupRecyclerViews()
        setupObservers()
        setupClickListeners()

        return root
    }

    override fun onResume() {
        super.onResume()
        // refresh homepage data every time user navigates back to homepage
        homeViewModel.refreshHomepageData()
    }

    private fun setupRecyclerViews() {
        // Setup ticket statistics adapter
        ticketStatisticsAdapter = TicketStatisticsAdapter { statistic ->
            navigateToTicketsWithStatus(statistic.value)
        }

        binding.recyclerTicketStatistics.apply {
            layoutManager = GridLayoutManager(requireContext(), 2)
            adapter = ticketStatisticsAdapter
        }

        // Setup groups adapter
        groupAdapter = HomeGroupAdapter { group ->
            navigateToTicketsWithGroup(group.id)
        }

        binding.recyclerGroups.apply {
            layoutManager = GridLayoutManager(requireContext(), 2)
            adapter = groupAdapter
        }

        // Setup navigation adapter
        navigationAdapter = NavigationAdapter { navigationItem ->
            findNavController().navigate(navigationItem.destinationId)
        }

        binding.recyclerNavigation.apply {
            layoutManager = GridLayoutManager(requireContext(), 2)
            adapter = navigationAdapter
        }

        // Set navigation items
        setupNavigationItems()
    }

    private fun setupObservers() {
        homeViewModel.ticketStatistics.observe(viewLifecycleOwner) { statistics ->
            ticketStatisticsAdapter.submitList(statistics)
        }

        homeViewModel.groups.observe(viewLifecycleOwner) { groups ->
            groupAdapter.submitList(groups)
        }

        homeViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            if (!isLoading) {
                binding.errorMessage.visibility = View.GONE
            }
        }
    }

    private fun setupClickListeners() {
        binding.cardAllTickets.setOnClickListener {
            findNavController().navigate(R.id.nav_tickets)
        }
    }

    private fun setupNavigationItems() {
        val navigationItems = mutableListOf<NavigationItem>()

        // Always add these items
        navigationItems.add(
            NavigationItem(
                id = "faqs",
                title = "سوالات متداول",
                iconRes = R.drawable.ic_faqs,
                foregroundColorRes = R.color.home_box_faqs_fg,
                backgroundColorRes = R.color.home_box_faqs_bg,
                destinationId = R.id.nav_faqs
            )
        )

        navigationItems.add(
            NavigationItem(
                id = "knowledges",
                title = "دانش‌ها",
                iconRes = R.drawable.ic_knowledges,
                foregroundColorRes = R.color.home_box_knowledges_fg,
                backgroundColorRes = R.color.home_box_knowledges_bg,
                destinationId = R.id.nav_knowledges
            )
        )

        navigationItems.add(
            NavigationItem(
                id = "events",
                title = "رویدادها",
                iconRes = R.drawable.ic_events,
                foregroundColorRes = R.color.home_box_events_fg,
                backgroundColorRes = R.color.home_box_events_bg,
                destinationId = R.id.nav_events
            )
        )

        navigationItems.add(
            NavigationItem(
                id = "profile",
                title = "پروفایل",
                iconRes = R.drawable.ic_profile,
                foregroundColorRes = R.color.home_box_profile_fg,
                backgroundColorRes = R.color.home_box_profile_bg,
                destinationId = R.id.nav_profile
            )
        )

        // Add Users item only for superusers
        val user = sessionManager.getUser()
        if (user != null && user.isSuperuser) {
            navigationItems.add(
                NavigationItem(
                    id = "users",
                    title = "کاربران",
                    iconRes = R.drawable.ic_users,
                    foregroundColorRes = R.color.home_box_users_fg,
                    backgroundColorRes = R.color.home_box_users_bg,
                    destinationId = R.id.nav_users
                )
            )
        }

        navigationAdapter.submitList(navigationItems)
    }

    private fun navigateToTicketsWithStatus(status: String) {
        Log.d("HomeFragment", "Navigate to tickets with status: $status")
        try {
            val action = HomeFragmentDirections.actionNavHomeToNavTickets(status = status, groupId = -1)
            findNavController().navigate(action)
        } catch (e: Exception) {
            Log.e("HomeFragment", "Navigation error", e)
            // Fallback to bundle navigation
            val bundle = Bundle().apply {
                putString("status", status)
            }
            findNavController().navigate(R.id.action_nav_home_to_nav_tickets, bundle)
        }
    }

    private fun navigateToTicketsWithGroup(groupId: Int) {
        Log.d("HomeFragment", "Navigate to tickets with group: $groupId")
        try {
            val action = HomeFragmentDirections.actionNavHomeToNavTickets(status = null, groupId = groupId)
            findNavController().navigate(action)
        } catch (e: Exception) {
            Log.e("HomeFragment", "Navigation error", e)
            // Fallback to bundle navigation
            val bundle = Bundle().apply {
                putInt("groupId", groupId)
            }
            findNavController().navigate(R.id.action_nav_home_to_nav_tickets, bundle)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
