package com.ariel.app.ui.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.data.model.EventGroup
import com.ariel.app.databinding.ItemHomeGroupBinding

class HomeGroupAdapter(
    private val onGroupClick: (EventGroup) -> Unit
) : ListAdapter<EventGroup, HomeGroupAdapter.ViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemHomeGroupBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ViewHolder(
        private val binding: ItemHomeGroupBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(group: EventGroup) {
            binding.tvGroupName.text = group.name

            binding.root.setOnClickListener {
                onGroupClick(group)
            }
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<EventGroup>() {
        override fun areItemsTheSame(oldItem: EventGroup, newItem: EventGroup): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: EventGroup, newItem: EventGroup): Boolean {
            return oldItem == newItem
        }
    }
}
