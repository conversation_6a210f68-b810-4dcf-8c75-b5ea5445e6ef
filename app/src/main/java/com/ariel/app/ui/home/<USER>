package com.ariel.app.ui.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.databinding.ItemNavigationBinding

class NavigationAdapter(
    private val onNavigationClick: (NavigationItem) -> Unit
) : ListAdapter<NavigationItem, NavigationAdapter.ViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemNavigationBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ViewHolder(
        private val binding: ItemNavigationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: NavigationItem) {
            binding.tvTitle.text = item.title
            binding.ivIcon.setImageResource(item.iconRes)
            
            val foregroundColor = ContextCompat.getColor(binding.root.context, item.foregroundColorRes)
            val backgroundColor = ContextCompat.getColor(binding.root.context, item.backgroundColorRes)
            
            binding.tvTitle.setTextColor(foregroundColor)
            binding.ivIcon.setColorFilter(foregroundColor)
            binding.root.setCardBackgroundColor(backgroundColor)

            binding.root.setOnClickListener {
                onNavigationClick(item)
            }
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<NavigationItem>() {
        override fun areItemsTheSame(oldItem: NavigationItem, newItem: NavigationItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: NavigationItem, newItem: NavigationItem): Boolean {
            return oldItem == newItem
        }
    }
}
