package com.ariel.app.ui.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.R
import com.ariel.app.data.model.TicketStatistics
import com.ariel.app.databinding.ItemTicketStatisticBinding

class TicketStatisticsAdapter(
    private val onStatisticClick: (TicketStatistics) -> Unit
) : ListAdapter<TicketStatistics, TicketStatisticsAdapter.ViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemTicketStatisticBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ViewHolder(
        private val binding: ItemTicketStatisticBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(statistic: TicketStatistics) {
            // Convert count to Persian numerals
            val persianCount = convertToPersianNumerals(statistic.count.toString())
            binding.tvCount.text = persianCount
            binding.tvLabel.text = statistic.label

            // Set colors based on value
            val colors = getColorsForValue(statistic.value)
            binding.tvCount.setTextColor(colors.first)
            binding.tvLabel.setTextColor(colors.first)
            binding.root.setCardBackgroundColor(colors.second)

            binding.root.setOnClickListener {
                onStatisticClick(statistic)
            }
        }

        private fun getColorsForValue(value: String): Pair<Int, Int> {
            return when (value) {
                "P" -> Pair(
                    ContextCompat.getColor(binding.root.context, R.color.status_p_fg),
                    ContextCompat.getColor(binding.root.context, R.color.status_p_bg)
                )
                "I" -> Pair(
                    ContextCompat.getColor(binding.root.context, R.color.status_i_fg),
                    ContextCompat.getColor(binding.root.context, R.color.status_i_bg)
                )
                "R" -> Pair(
                    ContextCompat.getColor(binding.root.context, R.color.status_r_fg),
                    ContextCompat.getColor(binding.root.context, R.color.status_r_bg)
                )
                "unvisited" -> Pair(
                    ContextCompat.getColor(binding.root.context, R.color.statistics_unvisited_fg),
                    ContextCompat.getColor(binding.root.context, R.color.statistics_unvisited_bg)
                )
                else -> Pair(
                    ContextCompat.getColor(binding.root.context, R.color.statistics_text_color),
                    ContextCompat.getColor(binding.root.context, R.color.surface)
                )
            }
        }

        private fun convertToPersianNumerals(input: String): String {
            val persianDigits = arrayOf('۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹')
            val englishDigits = arrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')

            var result = input
            for (i in englishDigits.indices) {
                result = result.replace(englishDigits[i], persianDigits[i])
            }
            return result
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<TicketStatistics>() {
        override fun areItemsTheSame(oldItem: TicketStatistics, newItem: TicketStatistics): Boolean {
            return oldItem.value == newItem.value
        }

        override fun areContentsTheSame(oldItem: TicketStatistics, newItem: TicketStatistics): Boolean {
            return oldItem == newItem
        }
    }
}
